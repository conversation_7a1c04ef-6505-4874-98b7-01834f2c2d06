% LEO卫星轨迹可视化脚本
% 根据satellite_coordinates.txt文件绘制卫星移动轨迹和地面站位置

clear; clc; close all;

% 读取数据文件
filename = 'satellite_coordinates.txt';
fid = fopen(filename, 'r');

if fid == -1
    error('无法打开文件: %s', filename);
end

% 初始化数据存储
satellite_data = [];
gateway_data = [];
ut_data = [];

% 逐行读取文件
while ~feof(fid)
    line = fgetl(fid);
    
    % 跳过注释行和空行
    if ischar(line) && ~isempty(line) && line(1) ~= '#'
        % 解析数据行
        data = sscanf(line, '%f %d %d %f %f %f %f %f %f');
        
        if length(data) >= 9
            time = data(1);
            nodeID = data(2);
            nodeType = data(3);
            x = data(4);
            y = data(5);
            z = data(6);
            lat = data(7);
            lon = data(8);
            alt = data(9);
            
            % 根据节点类型分类存储
            if nodeType == 0  % 卫星
                satellite_data = [satellite_data; time, x, y, z, lat, lon, alt];
            elseif nodeType == 1  % 网关
                gateway_data = [gateway_data; nodeID, x, y, z, lat, lon, alt];
            elseif nodeType == 2  % 用户终端
                ut_data = [ut_data; nodeID, x, y, z, lat, lon, alt];
            end
        end
    end
end

fclose(fid);

% 创建图形窗口
figure('Position', [100, 100, 1200, 800]);

% 子图1: 3D轨迹图
subplot(2, 2, 1);
hold on; grid on;

% 绘制真实地球纹理
earth_radius = 6371; % 地球半径 (km)
[X, Y, Z] = sphere(100); % 增加分辨率
X = X * earth_radius;
Y = Y * earth_radius;
Z = Z * earth_radius;

% 尝试加载地球纹理图像
try
    % 如果有地球纹理图像文件
    if exist('earth_texture.jpg', 'file') || exist('earth_texture.png', 'file')
        if exist('earth_texture.jpg', 'file')
            earth_img = imread('earth_texture.jpg');
        else
            earth_img = imread('earth_texture.png');
        end
        % 应用纹理
        surf(X, Y, Z, flipud(earth_img), 'FaceColor', 'texturemap', 'EdgeColor', 'none', 'FaceAlpha', 0.8);
    else
        % 创建渐变色地球效果
        [theta, phi] = meshgrid(linspace(0, 2*pi, size(X,2)), linspace(0, pi, size(X,1)));
        % 创建基于经纬度的颜色映射
        earth_colors = 0.3 + 0.4 * (sin(3*theta) .* cos(2*phi) + 1)/2;
        earth_colors = earth_colors + 0.2 * rand(size(earth_colors)); % 添加随机纹理
        surf(X, Y, Z, earth_colors, 'EdgeColor', 'none', 'FaceAlpha', 0.8);
        colormap(gca, [0.2 0.4 0.8; 0.1 0.6 0.2; 0.8 0.7 0.3; 0.6 0.4 0.2]); % 海洋、陆地、沙漠、山脉色彩
    end
catch
    % 如果加载失败，使用改进的默认地球
    earth_colors = 0.3 + 0.4 * sin(linspace(0, 4*pi, size(X,1)))' * cos(linspace(0, 2*pi, size(X,2)));
    surf(X, Y, Z, earth_colors, 'EdgeColor', 'none', 'FaceAlpha', 0.8);
    colormap(gca, [0.1 0.3 0.8; 0.2 0.6 0.3; 0.8 0.7 0.4]); % 蓝绿棕色调
end

% 绘制卫星轨迹
plot3(satellite_data(:,2), satellite_data(:,3), satellite_data(:,4), ...
      'r-', 'LineWidth', 2, 'DisplayName', '卫星轨迹');

% 标记起始和结束位置
plot3(satellite_data(1,2), satellite_data(1,3), satellite_data(1,4), ...
      'go', 'MarkerSize', 10, 'MarkerFaceColor', 'green', 'DisplayName', '起始位置');
plot3(satellite_data(end,2), satellite_data(end,3), satellite_data(end,4), ...
      'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'red', 'DisplayName', '结束位置');

% 绘制网关位置
if ~isempty(gateway_data)
    plot3(gateway_data(:,2), gateway_data(:,3), gateway_data(:,4), ...
          'bs', 'MarkerSize', 8, 'MarkerFaceColor', 'blue', 'DisplayName', '网关');
end

% 绘制用户终端位置
if ~isempty(ut_data)
    plot3(ut_data(:,2), ut_data(:,3), ut_data(:,4), ...
          'ms', 'MarkerSize', 6, 'MarkerFaceColor', 'magenta', 'DisplayName', '用户终端');
end

xlabel('X (km)'); ylabel('Y (km)'); zlabel('Z (km)');
title('LEO卫星3D轨迹图');
legend('Location', 'best');
axis equal;
view(45, 30);

% 子图2: XY平面投影
subplot(2, 2, 2);
hold on; grid on;

% 绘制地球投影圆
theta = linspace(0, 2*pi, 100);
plot(earth_radius*cos(theta), earth_radius*sin(theta), 'b--', 'LineWidth', 1);

plot(satellite_data(:,2), satellite_data(:,3), 'r-', 'LineWidth', 2);
plot(satellite_data(1,2), satellite_data(1,3), 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'green');
plot(satellite_data(end,2), satellite_data(end,3), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'red');

if ~isempty(gateway_data)
    plot(gateway_data(:,2), gateway_data(:,3), 'bs', 'MarkerSize', 6, 'MarkerFaceColor', 'blue');
end
if ~isempty(ut_data)
    plot(ut_data(:,2), ut_data(:,3), 'ms', 'MarkerSize', 4, 'MarkerFaceColor', 'magenta');
end

xlabel('X (km)'); ylabel('Y (km)');
title('XY平面投影');
axis equal;

% 子图3: 高度随时间变化
subplot(2, 2, 3);
plot(satellite_data(:,1), satellite_data(:,7), 'b-', 'LineWidth', 2);
grid on;
xlabel('时间 (s)'); ylabel('高度 (km)');
title('卫星高度随时间变化');

% 子图4: 经纬度轨迹
subplot(2, 2, 4);
hold on; grid on;

% 绘制世界地图轮廓（简化）
load coastlines;
plot(coastlon, coastlat, 'k-', 'LineWidth', 0.5);

% 绘制卫星地面轨迹
plot(satellite_data(:,6), satellite_data(:,5), 'r-', 'LineWidth', 2, 'DisplayName', '卫星轨迹');
plot(satellite_data(1,6), satellite_data(1,5), 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'green', 'DisplayName', '起始');
plot(satellite_data(end,6), satellite_data(end,5), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'red', 'DisplayName', '结束');

% 绘制地面站位置
if ~isempty(gateway_data)
    plot(gateway_data(:,6), gateway_data(:,5), 'bs', 'MarkerSize', 8, 'MarkerFaceColor', 'blue', 'DisplayName', '网关');
end
if ~isempty(ut_data)
    plot(ut_data(:,6), ut_data(:,5), 'ms', 'MarkerSize', 6, 'MarkerFaceColor', 'magenta', 'DisplayName', '用户终端');
end

xlabel('经度 (°)'); ylabel('纬度 (°)');
title('地面轨迹（经纬度）');
legend('Location', 'best');
xlim([-180, 180]); ylim([-90, 90]);

% 调整子图间距
sgtitle('LEO卫星轨迹分析 (60秒)', 'FontSize', 16, 'FontWeight', 'bold');

% 显示统计信息
fprintf('=== 轨迹统计信息 ===\n');
fprintf('总时长: %.1f 秒\n', max(satellite_data(:,1)));
fprintf('数据点数: %d\n', size(satellite_data, 1));
fprintf('高度范围: %.2f - %.2f km\n', min(satellite_data(:,7)), max(satellite_data(:,7)));
fprintf('纬度范围: %.2f° - %.2f°\n', min(satellite_data(:,5)), max(satellite_data(:,5)));
fprintf('经度范围: %.2f° - %.2f°\n', min(satellite_data(:,6)), max(satellite_data(:,6)));
fprintf('网关数量: %d\n', size(gateway_data, 1));
fprintf('用户终端数量: %d\n', size(ut_data, 1));

% 保存图像
print('satellite_trajectory_plot', '-dpng', '-r300');
fprintf('图像已保存为: satellite_trajectory_plot.png\n');
