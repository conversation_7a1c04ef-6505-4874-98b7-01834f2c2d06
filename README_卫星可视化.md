# LEO卫星轨迹可视化工具

这个工具包可以根据`satellite_coordinates.txt`文件中的卫星坐标数据，生成高质量的3D卫星轨迹可视化图表。

## 文件说明

### 主要脚本
1. **`plot_satellite_trajectory.m`** - 静态轨迹分析图
   - 生成包含4个子图的综合分析
   - 3D轨迹图（带真实地球纹理和3D卫星模型）
   - XY平面投影
   - 高度随时间变化曲线
   - 地面轨迹（经纬度图）

2. **`animate_satellite_trajectory_enhanced.m`** - 动画轨迹演示
   - 实时3D卫星轨迹动画
   - 地面轨迹动画
   - 真实地球纹理
   - 3D卫星和地面站模型
   - 实时信息显示

3. **`download_earth_texture.m`** - 地球纹理下载工具
   - 自动下载NASA地球纹理图像
   - 如果下载失败，会生成合成地球纹理

## 功能特点

### ✨ 真实地球纹理
- 支持NASA Blue Marble地球图像
- 自动下载高质量地球纹理
- 备用合成地球纹理生成

### 🛰️ 3D卫星模型
- 详细的3D卫星模型（主体、太阳能板、天线）
- 不同颜色标识起始/结束位置
- 动画中实时更新卫星位置

### 🏢 地面站可视化
- 3D地面站模型（网关和用户终端）
- 不同颜色和标签区分站点类型
- 在地图上显示准确位置

### 📊 多维度分析
- 3D空间轨迹
- 地面投影轨迹
- 高度变化曲线
- 速度计算和显示

## 使用方法

### 1. 运行静态分析
```matlab
% 在MATLAB命令窗口中运行
plot_satellite_trajectory
```

### 2. 运行动画演示
```matlab
% 在MATLAB命令窗口中运行
animate_satellite_trajectory_enhanced
```

### 3. 下载地球纹理（可选）
```matlab
% 手动下载地球纹理
download_earth_texture
```

## 数据格式

脚本会自动读取`satellite_coordinates.txt`文件，该文件包含：

- **卫星轨迹数据**：60秒内每秒的位置信息
- **网关位置**：2个固定地面网关
- **用户终端位置**：4个固定用户终端

数据格式：
```
时间(s) 节点ID 节点类型 X(km) Y(km) Z(km) 纬度(°) 经度(°) 高度(km)
```

节点类型：
- 0 = 卫星
- 1 = 网关(Gateway)
- 2 = 用户终端(UserTerminal)

## 输出文件

### 静态分析输出
- `satellite_trajectory_plot.png` - 高分辨率轨迹分析图
- 控制台统计信息（轨迹参数、节点数量等）

### 动画演示输出
- 实时动画窗口
- 进度信息显示
- 速度和位置实时更新

## 系统要求

- MATLAB R2018b或更高版本
- Image Processing Toolbox（用于图像处理）
- Mapping Toolbox（用于地图显示，可选）

## 自定义选项

### 修改动画速度
在`animate_satellite_trajectory_enhanced.m`中修改：
```matlab
animation_speed = 0.05; % 减小数值加快动画，增大数值减慢动画
```

### 修改轨迹尾巴长度
```matlab
trail_length = 15; % 显示的轨迹点数
```

### 修改卫星模型大小
```matlab
scale = 200; % 增大数值使模型更大
```

## 故障排除

### 1. 地球纹理显示问题
- 确保网络连接正常（用于下载纹理）
- 如果下载失败，脚本会自动使用合成纹理

### 2. 动画运行缓慢
- 减小地球球面分辨率：`sphere(30)` 改为 `sphere(20)`
- 增大动画速度：减小`animation_speed`值
- 减少轨迹尾巴长度：减小`trail_length`值

### 3. 内存不足
- 关闭其他MATLAB图形窗口
- 减小图像分辨率
- 分段播放动画

## 技术细节

### 坐标系统
- 使用ECEF（地心地固）坐标系
- 地球半径：6371 km
- 坐标单位：公里(km)

### 渲染技术
- 使用MATLAB的`patch`和`surf`函数
- 支持纹理映射
- 透明度和光照效果

### 性能优化
- 动态删除和重绘卫星模型
- 轨迹尾巴限制显示点数
- 分辨率自适应调整

## 扩展功能

可以进一步扩展的功能：
- 多卫星轨迹同时显示
- 卫星间通信链路可视化
- 覆盖范围分析
- 轨道参数计算
- 导出视频文件

---

**作者**: Augment Agent  
**版本**: 1.0  
**更新日期**: 2025-06-26
